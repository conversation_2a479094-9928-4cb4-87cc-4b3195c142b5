{"image_quality_assessment": {"image_path": "uploads\\user154_german_file_3.png", "assessment_method": "LLM", "model_used": "claude-3-5-sonnet-20241022", "timestamp": "2025-07-25T14:09:29.359Z", "quality_score": 50, "quality_level": "fair", "suitable_for_extraction": true, "blur_detection": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Blur assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "contrast_assessment": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Contrast assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "glare_identification": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Glare assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "water_stains": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Water stain assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "tears_or_folds": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Tear/fold assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "cut_off_detection": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Cut-off assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "missing_sections": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Missing section assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "obstructions": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Obstruction assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "overall_quality_score": 5}, "classification": {"is_expense": false, "expense_type": null, "language": "unknown", "language_confidence": 0, "document_location": "unknown", "expected_location": "unknown", "location_match": false, "error_type": "classification_error", "error_message": "rawContent.substring is not a function", "classification_confidence": 0, "reasoning": "Classification failed due to error: rawContent.substring is not a function", "schema_field_analysis": {"fields_found": [], "fields_missing": [], "total_fields_found": 0, "expense_identification_reasoning": "Classification failed due to system error"}}, "extraction": {"vendor_name": "extraction_failed", "notes": "Error: rawContent.substring is not a function"}, "compliance": {"validation_result": {"is_valid": false, "issues_count": 1, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "system_error", "description": "Compliance analysis failed: rawContent.substring is not a function", "recommendation": "Please retry the compliance analysis or contact support.", "knowledge_base_reference": "System error during analysis"}], "corrected_receipt": null, "compliance_summary": "Analysis failed due to system error"}, "technical_details": {"content_type": "expense_receipt", "country": "unknown", "icp": "unknown", "receipt_type": "unknown", "issues_count": 1}}, "citations": {"citations": {}, "metadata": {"total_fields_analyzed": 0, "fields_with_field_citations": 0, "fields_with_value_citations": 0, "average_confidence": 0}}, "timing": {"phase_timings": {"image_quality_assessment_minutes": "0.27", "file_classification_minutes": "0.16", "data_extraction_minutes": "0.20", "issue_detection_minutes": "0.23", "citation_generation_minutes": "0.12"}, "agent_performance": {"image_quality_assessment": {"start_time": "2025-07-25T14:09:13.399Z", "end_time": "2025-07-25T14:09:29.358Z", "duration_minutes": "0.27", "model_used": "claude-3-5-sonnet-20241022"}, "file_classification": {"start_time": "2025-07-25T14:09:29.359Z", "end_time": "2025-07-25T14:09:39.034Z", "duration_minutes": "0.16", "model_used": "claude-3-5-sonnet-20241022"}, "data_extraction": {"start_time": "2025-07-25T14:09:39.035Z", "end_time": "2025-07-25T14:09:50.736Z", "duration_minutes": "0.20", "model_used": "claude-3-5-sonnet-20241022"}, "issue_detection": {"start_time": "2025-07-25T14:09:50.736Z", "end_time": "2025-07-25T14:10:04.602Z", "duration_minutes": "0.23", "model_used": "claude-3-5-sonnet-20241022"}, "citation_generation": {"start_time": "2025-07-25T14:10:04.603Z", "end_time": "2025-07-25T14:10:11.839Z", "duration_minutes": "0.12", "model_used": "claude-3-5-sonnet-20241022"}}, "total_processing_time_minutes": "0.97"}, "metadata": {"filename": "german_file_3.png", "processing_time": 58441, "country": "Germany", "icp": "Global People", "processed_at": "2025-07-25T14:10:11.839Z"}}