{"image_quality_assessment": {"image_path": "uploads\\user155_german_file_3.png", "assessment_method": "LLM", "model_used": "claude-3-5-sonnet-20241022", "timestamp": "2025-07-25T14:16:50.897Z", "quality_score": 50, "quality_level": "fair", "suitable_for_extraction": true, "blur_detection": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Blur assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "contrast_assessment": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Contrast assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "glare_identification": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Glare assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "water_stains": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Water stain assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "tears_or_folds": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Tear/fold assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "cut_off_detection": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Cut-off assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "missing_sections": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Missing section assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "obstructions": {"detected": false, "severity_level": "low", "confidence_score": 0.5, "quantitative_measure": 0, "description": "Obstruction assessment failed", "recommendation": "Manual review recommended due to assessment failure"}, "overall_quality_score": 5}, "classification": {"is_expense": false, "expense_type": null, "language": "unknown", "language_confidence": 0, "document_location": "unknown", "expected_location": "unknown", "location_match": false, "error_type": "classification_error", "error_message": "[\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"array\",\n    \"path\": [],\n    \"message\": \"Expected object, received array\"\n  }\n]", "classification_confidence": 0, "reasoning": "Classification failed due to error: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"array\",\n    \"path\": [],\n    \"message\": \"Expected object, received array\"\n  }\n]", "schema_field_analysis": {"fields_found": [], "fields_missing": [], "total_fields_found": 0, "expense_identification_reasoning": "Classification failed due to system error"}}, "extraction": {"vendor_name": "extraction_failed", "notes": "Error: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"array\",\n    \"path\": [],\n    \"message\": \"Expected object, received array\"\n  }\n]"}, "compliance": {"validation_result": {"is_valid": false, "issues_count": 1, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "system_error", "description": "Compliance analysis failed: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"array\",\n    \"path\": [],\n    \"message\": \"Expected object, received array\"\n  }\n]", "recommendation": "Please retry the compliance analysis or contact support.", "knowledge_base_reference": "System error during analysis"}], "corrected_receipt": null, "compliance_summary": "Analysis failed due to system error"}, "technical_details": {"content_type": "expense_receipt", "country": "unknown", "icp": "unknown", "receipt_type": "unknown", "issues_count": 1}}, "citations": {"citations": {}, "metadata": {"total_fields_analyzed": 0, "fields_with_field_citations": 0, "fields_with_value_citations": 0, "average_confidence": 0}}, "timing": {"phase_timings": {"image_quality_assessment_minutes": "0.19", "file_classification_minutes": "0.21", "data_extraction_minutes": "0.24", "issue_detection_minutes": "0.22", "citation_generation_minutes": "0.17"}, "agent_performance": {"image_quality_assessment": {"start_time": "2025-07-25T14:16:39.243Z", "end_time": "2025-07-25T14:16:50.897Z", "duration_minutes": "0.19", "model_used": "claude-3-5-sonnet-20241022"}, "file_classification": {"start_time": "2025-07-25T14:16:50.898Z", "end_time": "2025-07-25T14:17:03.749Z", "duration_minutes": "0.21", "model_used": "claude-3-5-sonnet-20241022"}, "data_extraction": {"start_time": "2025-07-25T14:17:03.750Z", "end_time": "2025-07-25T14:17:18.051Z", "duration_minutes": "0.24", "model_used": "claude-3-5-sonnet-20241022"}, "issue_detection": {"start_time": "2025-07-25T14:17:18.052Z", "end_time": "2025-07-25T14:17:31.208Z", "duration_minutes": "0.22", "model_used": "claude-3-5-sonnet-20241022"}, "citation_generation": {"start_time": "2025-07-25T14:17:31.209Z", "end_time": "2025-07-25T14:17:41.521Z", "duration_minutes": "0.17", "model_used": "claude-3-5-sonnet-20241022"}}, "total_processing_time_minutes": "1.04"}, "metadata": {"filename": "german_file_3.png", "processing_time": 62280, "country": "Germany", "icp": "Global People", "processed_at": "2025-07-25T14:17:41.522Z"}}